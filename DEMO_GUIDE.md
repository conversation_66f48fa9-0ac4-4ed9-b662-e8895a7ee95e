# 聊天机器人新功能演示指南

## 🚀 如何测试新功能

### 1. 启动应用
```bash
npm run dev
```

访问 `http://localhost:5174`

### 2. 测试 Markdown 支持

在聊天框中输入以下内容来测试 Markdown 渲染：

```markdown
# 测试 Markdown 功能

这是一个 **粗体** 文本和 *斜体* 文本的测试。

## 代码示例

```javascript
function greet(name) {
  console.log(`Hello, ${name}!`);
}
```

## 列表测试

- 第一项
- 第二项
- 第三项

## 表格测试

| 功能 | 状态 | 描述 |
|------|------|------|
| Markdown | ✅ | 完全支持 |
| 工具调用 | ✅ | 卡片展示 |
| 代码高亮 | ✅ | 语法高亮 |

[点击这里访问 Google](https://google.com)
```

### 3. 测试工具调用功能

发送以下消息来触发搜索工具：

```
请帮我搜索关于 React Hooks 的最新教程和最佳实践
```

或者：

```
搜索一下 TypeScript 的新特性
```

### 4. 观察新的 UI 变化

#### 工具调用展示
- 🔧 工具调用信息会显示在助手回复的上方
- 📁 默认处于折叠状态，点击可展开
- 🏷️ 显示工具名称和参数信息

#### 搜索结果卡片
- 🖼️ 如果有图片会显示缩略图
- 📝 显示文章标题和 URL
- 👤 显示作者和发布日期（如果有）
- 💬 鼠标悬停显示内容摘要
- 🔗 点击卡片跳转到原始页面

#### Markdown 渲染
- 📖 用户消息和助手回复都支持 Markdown
- 🎨 代码块有语法高亮
- 📊 表格、列表等格式正确显示

## 🎯 测试场景

### 场景 1：技术文档查询
```
# 我想学习 React

请帮我搜索以下内容：
- React 18 的新特性
- **Hooks** 的最佳实践
- 性能优化技巧

请提供一些高质量的教程链接。
```

### 场景 2：新闻搜索
```
搜索最新的人工智能发展动态，特别是关于大语言模型的新闻
```

### 场景 3：代码示例
```
请搜索 TypeScript 泛型的使用示例，并用代码块展示一些常见的模式：

```typescript
// 期望看到类似这样的示例
interface ApiResponse<T> {
  data: T;
  status: number;
}
```
```

## 🔍 功能验证清单

### ✅ Markdown 渲染
- [ ] 标题（H1-H6）正确显示
- [ ] 粗体和斜体文本正确渲染
- [ ] 代码块有语法高亮
- [ ] 行内代码正确显示
- [ ] 列表（有序和无序）正确显示
- [ ] 表格正确渲染
- [ ] 链接可以点击

### ✅ 工具调用展示
- [ ] 工具调用部分显示在助手回复上方
- [ ] 默认处于折叠状态
- [ ] 可以点击展开/折叠
- [ ] 显示工具名称和参数
- [ ] 搜索结果以卡片形式展示

### ✅ 搜索结果卡片
- [ ] 显示文章标题
- [ ] 显示 URL 链接
- [ ] 显示图片（如果有）
- [ ] 显示作者和日期（如果有）
- [ ] 鼠标悬停显示摘要
- [ ] 点击卡片跳转到原始页面
- [ ] 卡片布局响应式（移动端友好）

### ✅ 交互体验
- [ ] 页面加载速度正常
- [ ] 滚动到底部功能正常
- [ ] 工具调用状态指示器正常
- [ ] 消息发送和接收流畅

## 🐛 已知问题和限制

1. **图片加载**: 某些网站的图片可能因为 CORS 策略无法显示
2. **长文本**: 非常长的搜索结果摘要可能需要更好的截断处理
3. **移动端**: 在小屏幕上卡片布局可能需要进一步优化

## 📱 移动端测试

在移动设备或浏览器开发者工具的移动模式下测试：
- 卡片是否正确堆叠
- 悬停效果在触摸设备上的表现
- 文本是否可读
- 按钮是否容易点击

## 🎨 自定义样式

如果需要调整样式，主要文件：
- `app/tailwind.css` - 全局样式和 Markdown 样式
- `app/components/ToolResultCard.tsx` - 卡片样式
- `app/components/ToolCallSection.tsx` - 工具调用部分样式

## 📊 性能监控

观察以下指标：
- 页面首次加载时间
- Markdown 渲染性能
- 大量搜索结果的渲染性能
- 内存使用情况（长时间使用后）
